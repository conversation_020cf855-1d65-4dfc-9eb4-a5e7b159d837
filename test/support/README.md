# Ecto Test Setup for Operations Features

This directory contains the test setup for using Ecto with SQLite in-memory mode for testing Operations features in the Drops library.

## Overview

The setup provides:
- SQLite in-memory database for fast, isolated tests
- Ecto SQL sandbox for transaction-based test isolation
- Sample User schema with name and email fields
- Helper functions for common database operations

## Files

- `test_repo.ex` - Test repository configuration
- `user.ex` - Sample User schema with validations
- `data_case.ex` - Test case template for database tests
- `migrations/` - Database migrations (table creation)

## Usage

### Basic Test Setup

To use Ecto in your tests, use the `Drops.DataCase` module:

```elixir
defmodule MyOperationsTest do
  use Drops.DataCase

  alias Drops.User

  test "can create and query users" do
    # Create a user
    {:ok, user} = 
      %User{}
      |> User.changeset(%{name: "<PERSON>", email: "<EMAIL>"})
      |> TestRepo.insert()

    # Query the user
    found_user = TestRepo.get(User, user.id)
    assert found_user.name == "<PERSON>"
  end
end
```

### Available Operations

The setup supports all standard Ecto operations:

- `TestRepo.insert/1` - Insert records
- `TestRepo.update/1` - Update records  
- `TestRepo.delete/1` - Delete records
- `TestRepo.get/2` - Get by ID
- `TestRepo.all/1` - Get all records
- `TestRepo.transaction/1` - Run in transaction

### User Schema

The User schema includes:
- `name` (string, required)
- `email` (string, required, unique, validated format)
- `inserted_at` and `updated_at` timestamps

### Validation Examples

```elixir
# Valid user
changeset = User.changeset(%User{}, %{name: "Jane", email: "<EMAIL>"})
assert changeset.valid?

# Invalid email format
changeset = User.changeset(%User{}, %{name: "Jane", email: "invalid"})
refute changeset.valid?
assert "must be a valid email" in errors_on(changeset).email
```

### Transaction Examples

```elixir
result = TestRepo.transaction(fn ->
  {:ok, user1} = create_user(%{name: "User 1", email: "<EMAIL>"})
  {:ok, user2} = create_user(%{name: "User 2", email: "<EMAIL>"})
  [user1, user2]
end)
```

## Test Isolation

Each test runs in its own database transaction that is rolled back after the test completes, ensuring complete isolation between tests. The SQLite database is in-memory, so it's created fresh for each test run.

## Dependencies

The following dependencies are added to `mix.exs` for test environment only:

```elixir
{:ecto, "~> 3.10", only: :test},
{:ecto_sql, "~> 3.10", only: :test},
{:ecto_sqlite3, "~> 0.12", only: :test}
```

## Running Tests

```bash
# Run all tests
mix test

# Run specific test file
mix test test/drops/user_test.exs

# Run with database query logging
mix test --trace
```
