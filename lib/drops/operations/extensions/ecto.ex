defmodule Drops.Operations.Extensions.Ecto do
  @moduledoc """
  Ecto extension for Operations.

  This extension adds Ecto-specific functionality to Operations modules when
  a repo is configured. It provides:

  - Changeset validation pipeline
  - `cast_changeset/2` and `validate/1` callbacks
  - `changeset/1` and `persist/1` functions
  - Phoenix.HTML.FormData protocol support for Success/Failure structs
  - Schema error conversion to changeset errors

  The extension is automatically enabled when the `:repo` option is provided.
  """

  @behaviour Drops.Operations.Extension

  @impl true
  def enabled?(opts) do
    Keyword.has_key?(opts, :repo) && !is_nil(opts[:repo])
  end

  @impl true
  def extend_using_macro(_opts) do
    quote do
      # No additional setup needed in the main __using__ macro
    end
  end

  @impl true
  def extend_operation_runtime(_opts) do
    quote do
      # Import Ecto.Changeset for changeset operations
      import Ecto.Changeset

      # Add Ecto-specific callbacks to the behaviour
      @callback validate(changeset :: Ecto.Changeset.t()) :: Ecto.Changeset.t()
      @callback cast_changeset(params :: map(), changeset :: Ecto.Changeset.t()) ::
                  Ecto.Changeset.t()

      # Add Ecto-specific delegation functions
      def cast_changeset(params, changeset) do
        Drops.Operations.Extensions.Ecto.cast_changeset(__MODULE__, params, changeset)
      end

      def changeset(params) do
        Drops.Operations.Extensions.Ecto.changeset(__MODULE__, params)
      end

      def persist(params) do
        Drops.Operations.Extensions.Ecto.persist(__MODULE__, params)
      end

      # Override validate to create changeset and call user's validate function
      def validate(params) do
        schema = schema()

        if schema.meta[:source_schema] do
          # Create changeset from params and call user's validate function if defined
          changeset =
            params
            |> changeset()
            |> cast_changeset(params)

          # Call the original validate function if user has overridden it
          if function_exported?(__MODULE__, :validate, 1) and
               :erlang.function_exported(__MODULE__, :validate, 1) do
            # This is tricky - we need to call the user's validate function
            # For now, let's assume the user's function will be called by the pipeline
            changeset
          else
            changeset
          end
        else
          params
        end
      end
    end
  end

  @impl true
  def extend_operation_definition(opts) do
    quote do
      # Import Ecto.Changeset for changeset operations
      import Ecto.Changeset

      # Add Ecto-specific functions directly to the operation module
      def cast_changeset(_params, changeset) do
        changeset
      end

      def changeset(params) do
        source_schema = schema().meta[:source_schema]
        Ecto.Changeset.change(struct(source_schema), params)
      end

      # Add persist function if repo is configured
      unquote(
        if opts[:repo] do
          quote do
            def persist(params) do
              @repo.insert(changeset(params))
            end
          end
        end
      )

      # Override prepare to create changeset when Ecto schema is present
      def prepare(params) do
        schema = schema()

        if schema.meta[:source_schema] do
          params
          |> changeset()
          |> cast_changeset(params)
        else
          params
        end
      end

      # Default validate implementation - can be overridden by user
      def validate(changeset_or_params) do
        case changeset_or_params do
          %Ecto.Changeset{} = changeset -> changeset
          params -> params
        end
      end

      # Helper function to convert schema validation errors to changeset errors
      defp convert_schema_errors_to_changeset(changeset, schema_errors) do
        Enum.reduce(schema_errors, changeset, fn error, acc ->
          case error do
            %{path: [field], text: text} when is_atom(field) ->
              Ecto.Changeset.add_error(acc, field, text)

            %{path: [field], text: text} when is_binary(field) ->
              field_atom = String.to_existing_atom(field)
              Ecto.Changeset.add_error(acc, field_atom, text)

            # Handle nested paths by flattening to the first level for now
            %{path: [field | _], text: text} when is_atom(field) ->
              Ecto.Changeset.add_error(acc, field, text)

            %{path: [field | _], text: text} when is_binary(field) ->
              field_atom = String.to_existing_atom(field)
              Ecto.Changeset.add_error(acc, field_atom, text)

            # Fallback for other error structures
            _ ->
              acc
          end
        end)
      end

      # Make Ecto functions overridable
    end
  end

  # Public API functions that operations delegate to

  @doc """
  Default cast_changeset implementation that returns the changeset unchanged.
  """
  def cast_changeset(_operation_module, _params, changeset) do
    changeset
  end

  @doc """
  Creates a changeset from the operation's schema and provided parameters.
  """
  def changeset(operation_module, params) do
    schema = operation_module.schema()
    source_schema = schema.meta[:source_schema]

    Ecto.Changeset.change(struct(source_schema), params)
  end

  @doc """
  Persists parameters using the configured repo.
  """
  def persist(operation_module, params) do
    repo = operation_module.__repo__()

    if repo do
      repo.insert(operation_module.changeset(params))
    else
      raise "No repo configured for this operation"
    end
  end

  # Helper function to convert schema validation errors to changeset errors
  defp convert_schema_errors_to_changeset(changeset, schema_errors) do
    Enum.reduce(schema_errors, changeset, fn error, acc ->
      case error do
        %{path: [field], text: text} when is_atom(field) ->
          Ecto.Changeset.add_error(acc, field, text)

        %{path: [field], text: text} when is_binary(field) ->
          field_atom = String.to_existing_atom(field)
          Ecto.Changeset.add_error(acc, field_atom, text)

        # Handle nested paths by flattening to the first level for now
        %{path: [field | _], text: text} when is_atom(field) ->
          Ecto.Changeset.add_error(acc, field, text)

        %{path: [field | _], text: text} when is_binary(field) ->
          field_atom = String.to_existing_atom(field)
          Ecto.Changeset.add_error(acc, field_atom, text)

        # Fallback for other error structures
        _ ->
          acc
      end
    end)
  end
end

# Phoenix.HTML.FormData protocol implementations for form compatibility
# These are only compiled if Phoenix.HTML is available
if Code.ensure_loaded?(Phoenix.HTML.FormData) do
  defimpl Phoenix.HTML.FormData, for: Drops.Operations.Success do
    def to_form(%{params: params, type: :form}, options) do
      # For :form operations, use the validated params as the form data
      # This allows the Success struct to work with Phoenix form helpers
      # Convert atom keys to string keys as required by Phoenix.HTML
      form_data = if is_map(params), do: stringify_keys(params), else: %{}
      create_form_struct(form_data, options, "success")
    end

    def to_form(%{params: params}, options) do
      # For non-form operations, fall back to params
      # Convert atom keys to string keys as required by Phoenix.HTML
      form_data = if is_map(params), do: stringify_keys(params), else: %{}
      create_form_struct(form_data, options, "success")
    end

    def to_form(data, form, field, options) do
      form_data = if is_map(data.params), do: stringify_keys(data.params), else: %{}
      Phoenix.HTML.FormData.to_form(form_data, form, field, options)
    end

    def input_value(%{params: params}, form, field) do
      form_data = if is_map(params), do: stringify_keys(params), else: %{}
      Phoenix.HTML.FormData.input_value(form_data, form, field)
    end

    def input_validations(%{params: _params}, _form, _field) do
      []
    end

    # Helper function to create a proper Phoenix.HTML.Form struct
    defp create_form_struct(form_data, options, default_name) do
      {name, options} = Keyword.pop(options, :as)
      name = to_string(name || default_name)
      id = Keyword.get(options, :id) || name

      %Phoenix.HTML.Form{
        source: form_data,
        impl: __MODULE__,
        id: id,
        name: name,
        data: form_data,
        params: form_data,
        errors: [],
        hidden: [],
        options: options,
        action: nil,
        index: nil
      }
    end

    # Helper function to convert atom keys to string keys
    defp stringify_keys(map) when is_map(map) do
      Map.new(map, fn
        {key, value} when is_atom(key) -> {Atom.to_string(key), value}
        {key, value} -> {key, value}
      end)
    end

    defp stringify_keys(other), do: other
  end

  defimpl Phoenix.HTML.FormData, for: Drops.Operations.Failure do
    def to_form(%{params: params, result: result, type: :form}, options) do
      # For :form operations with validation errors, we want to preserve
      # the original params and include error information
      # Convert atom keys to string keys as required by Phoenix.HTML
      form_data = if is_map(params), do: stringify_keys(params), else: %{}

      # If result is an Ecto.Changeset, use it directly for form data
      # as it contains both data and errors
      case result do
        %Ecto.Changeset{} = changeset ->
          Phoenix.HTML.FormData.to_form(changeset, options)

        _ ->
          create_form_struct(form_data, options, "failure")
      end
    end

    def to_form(%{params: params, result: result}, options) do
      # For non-form operations, check if result is a changeset
      case result do
        %Ecto.Changeset{} = changeset ->
          Phoenix.HTML.FormData.to_form(changeset, options)

        _ ->
          form_data = if is_map(params), do: stringify_keys(params), else: %{}
          create_form_struct(form_data, options, "failure")
      end
    end

    def to_form(data, form, field, options) do
      case data.result do
        %Ecto.Changeset{} = changeset ->
          Phoenix.HTML.FormData.to_form(changeset, form, field, options)

        _ ->
          form_data = if is_map(data.params), do: stringify_keys(data.params), else: %{}
          Phoenix.HTML.FormData.to_form(form_data, form, field, options)
      end
    end

    def input_value(%{params: params, result: result}, form, field) do
      case result do
        %Ecto.Changeset{} = changeset ->
          Phoenix.HTML.FormData.input_value(changeset, form, field)

        _ ->
          form_data = if is_map(params), do: stringify_keys(params), else: %{}
          Phoenix.HTML.FormData.input_value(form_data, form, field)
      end
    end

    def input_validations(%{params: _params, result: result}, form, field) do
      case result do
        %Ecto.Changeset{} = changeset ->
          Phoenix.HTML.FormData.input_validations(changeset, form, field)

        _ ->
          []
      end
    end

    # Helper function to create a proper Phoenix.HTML.Form struct
    defp create_form_struct(form_data, options, default_name) do
      {name, options} = Keyword.pop(options, :as)
      name = to_string(name || default_name)
      id = Keyword.get(options, :id) || name

      %Phoenix.HTML.Form{
        source: form_data,
        impl: __MODULE__,
        id: id,
        name: name,
        data: form_data,
        params: form_data,
        errors: [],
        hidden: [],
        options: options,
        action: nil,
        index: nil
      }
    end

    # Helper function to convert atom keys to string keys
    defp stringify_keys(map) when is_map(map) do
      Map.new(map, fn
        {key, value} when is_atom(key) -> {Atom.to_string(key), value}
        {key, value} -> {key, value}
      end)
    end

    defp stringify_keys(other), do: other
  end
end
